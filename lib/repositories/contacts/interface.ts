// contact.ts

import { SessionContext } from "../auth/types"
import { LogicalFilter } from "../LogicalFilter"

export interface Contact {
  id: string
  name: string
  phone: string
  email?: string
  tags?: string[]
  notes?: { text: string; createdAt: string }[]
  isTesting?: boolean

  createdAt: Date
  updatedAt: Date
  deletedAt?: Date
  createdBy?: string
  updatedBy?: string
  organizationId?: string
}

export interface ContactCreateInput {
  name: string
  phone: string
  email?: string
  tags?: string[]
  notes?: { text: string; createdAt: string }[]
  isTesting?: boolean
}

export interface ContactUpdateInput {
  name?: string
  phone?: string
  email?: string
  tags?: string[]
  notes?: { text: string; createdAt: string }[]
}

export interface ContactQueryParams {
  search?: string
  filters?: { field: keyof Contact | string; value: any }[]
  filterEnhanced?: LogicalFilter<Contact>
  sort?: { field: keyof Contact | string; direction: "ASC" | "DESC" }[]
  page?: number
  limit?: number
  includeDeleted?: boolean
}

export interface ContactBusinessLogicInterface {
  getById(
    id: string,
    context: SessionContext,
    includeDeleted?: boolean,
  ): Promise<Contact | null>
  getAll(
    params: ContactQueryParams,
    context: SessionContext,
  ): Promise<{
    items: Contact[]
    total: number
  }>
  getByPhone(phone: string, context: SessionContext): Promise<Contact | null>
  create(data: ContactCreateInput, context: SessionContext): Promise<Contact>
  update(
    id: string,
    data: ContactUpdateInput,
    context: SessionContext,
  ): Promise<Contact | null>
  delete(
    id: string,
    context: SessionContext,
    hardDelete?: boolean,
  ): Promise<boolean>
  restore(id: string, context: SessionContext): Promise<boolean>

  bulkCreate(
    data: ContactCreateInput[],
    context: SessionContext,
  ): Promise<Contact[]>
  bulkUpdate(
    updates: { id: string; data: ContactUpdateInput }[],
    context: SessionContext,
  ): Promise<number>
  bulkDelete(
    ids: string[],
    context: SessionContext,
    hardDelete?: boolean,
  ): Promise<number>
}
